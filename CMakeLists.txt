cmake_minimum_required(VERSION 3.21)

# Set the C++ standard
if (NOT DEFINED CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 23)
endif()

# Disable compiler extensions for better portability
set(CMAKE_CXX_EXTENSIONS OFF)

# Export compile commands for IDE integration
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Create symlink to compile_commands.json in project root for clangd
if(CMAKE_EXPORT_COMPILE_COMMANDS)
  add_custom_target(
    symlink_compile_commands ALL
    COMMAND ${CMAKE_COMMAND} -E create_symlink
      ${CMAKE_BINARY_DIR}/compile_commands.json
      ${CMAKE_SOURCE_DIR}/compile_commands.json
    DEPENDS ${CMAKE_BINARY_DIR}/compile_commands.json
    COMMENT "Creating symlink to compile_commands.json in project root"
  )
endif()

# Set the project name and language
project(
  cmake_template
  VERSION 0.0.1
  DESCRIPTION "CMake template"
  LANGUAGES CXX)

# Prevent in-source builds
if(CMAKE_SOURCE_DIR STREQUAL CMAKE_BINARY_DIR)
  message(FATAL_ERROR "In-source builds are not allowed. Please create a separate build directory.")
endif()

# Set default build type to Release if not specified
if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
  set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the type of build." FORCE)
  set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# Create interface libraries for project options and warnings
add_library(cmake_template_options INTERFACE)
add_library(cmake_template_warnings INTERFACE)

# Add compiler warnings
target_compile_options(cmake_template_warnings INTERFACE
  $<$<CXX_COMPILER_ID:MSVC>:/W4>
  $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# Set C++ standard for the options target
target_compile_features(cmake_template_options INTERFACE cxx_std_${CMAKE_CXX_STANDARD})

# Create aliases for easier usage
add_library(cmake_template::cmake_template_options ALIAS cmake_template_options)
add_library(cmake_template::cmake_template_warnings ALIAS cmake_template_warnings)

# Add include directory
target_include_directories(cmake_template_options INTERFACE
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

# Add the src directory
add_subdirectory(src)

# Only build tests if this is the top-level project
if(PROJECT_IS_TOP_LEVEL)
  # Enable testing
  include(CTest)

  if(BUILD_TESTING)
    add_subdirectory(test)
  endif()
endif()
